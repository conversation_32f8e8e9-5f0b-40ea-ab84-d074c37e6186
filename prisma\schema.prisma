generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                   String                 @id @default(cuid())
  email                String                 @unique
  name                 String
  password             String
  role                 String                 @default("user")
  branchId             String?
  isActive             Boolean                @default(true)
  lastLogin            DateTime?
  createdAt            DateTime               @default(now())
  updatedAt            DateTime               @updatedAt
  failedLoginAttempts  Int                    @default(0)
  lockedUntil          DateTime?
  username             String                 @unique
  image                String?
  preferences          Json?
  activityLogs         ActivityLog[]
  notifications        InAppNotification[]    @relation("UserNotifications")
  pushSubscriptions    PushSubscription[]
  reportComments       ReportComment[]
  telegramLinkingCodes TelegramLinkingCode[]
  telegramSubscription TelegramSubscription?
  branch               Branch?                @relation(fields: [branchId], references: [id])
  activities           UserActivity[]
  branchAssignments    UserBranchAssignment[]
  userRoles            UserRole[]

  @@index([branchId])
}

model UserBranchAssignment {
  id        String   @id @default(cuid())
  userId    String
  branchId  String
  isDefault Bo<PERSON>an  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  branch    Branch   @relation(fields: [branchId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, branchId])
  @@index([userId])
}

model Branch {
  id                String                 @id @default(cuid())
  code              String                 @unique
  name              String
  isActive          Boolean                @default(true)
  createdAt         DateTime               @default(now())
  updatedAt         DateTime               @updatedAt
  parentId          String?
  parent            Branch?                @relation("BranchHierarchy", fields: [parentId], references: [id])
  children          Branch[]               @relation("BranchHierarchy")
  reports           Report[]
  users             User[]
  branchAssignments UserBranchAssignment[]
  userRoles         UserRole[]
}

model Report {
  id            String          @id @default(cuid())
  branchId      String
  writeOffs     Float
  ninetyPlus    Float
  reportType    String          @default("actual")
  status        String          @default("pending")
  submittedBy   String
  comments      String?
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  planReportId  String?
  submittedAt   String
  date          DateTime        @db.Date
  branch        Branch          @relation(fields: [branchId], references: [id])
  planReport    Report?         @relation("PlanActualRelation", fields: [planReportId], references: [id])
  actualReports Report[]        @relation("PlanActualRelation")
  ReportComment ReportComment[]

  @@unique([date, branchId, reportType])
  @@index([branchId, date])
  @@index([date, status])
  @@index([planReportId])
}

model ActivityLog {
  id        String   @id @default(cuid())
  userId    String
  action    String
  details   String?
  ipAddress String?
  userAgent String?
  timestamp DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Role {
  id          String     @id @default(cuid())
  name        String     @unique
  description String?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  userRoles   UserRole[]
}

model UserRole {
  id        String   @id @default(cuid())
  userId    String
  roleId    String
  branchId  String?
  isDefault Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  branch    Branch?  @relation(fields: [branchId], references: [id])
  role      Role     @relation(fields: [roleId], references: [id])
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId, branchId])
  @@index([userId])
}

model UserActivity {
  id        String   @id @default(cuid())
  userId    String
  action    String
  details   Json
  ipAddress String
  userAgent String
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([action])
  @@index([createdAt])
}

model OrganizationSettings {
  id              String   @id @default(cuid())
  organizationId  String   @unique
  validationRules Json     @default("{\"comments\": {\"required\": true, \"minLength\": 10}, \"writeOffs\": {\"maxAmount\": 1000, \"requireApproval\": true}, \"ninetyPlus\": {\"maxAmount\": 5000, \"requireApproval\": true}, \"duplicateCheck\": {\"enabled\": true}}")
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
}

model PushSubscription {
  id        String   @id @default(cuid())
  endpoint  String   @unique
  p256dh    String
  auth      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  userId    String?
  user      User?    @relation(fields: [userId], references: [id])

  @@index([endpoint])
  @@index([userId])
}

model InAppNotification {
  id        String              @id @default(cuid())
  userId    String
  title     String
  body      String
  type      String
  data      Json?
  isRead    Boolean             @default(false)
  createdAt DateTime            @default(now())
  readAt    DateTime?
  actionUrl String?
  user      User                @relation("UserNotifications", fields: [userId], references: [id], onDelete: Cascade)
  events    NotificationEvent[]

  @@index([userId])
  @@index([type])
  @@index([createdAt])
  @@index([isRead])
}

model NotificationEvent {
  id             String            @id @default(cuid())
  notificationId String
  event          String
  metadata       Json?
  timestamp      DateTime          @default(now())
  notification   InAppNotification @relation(fields: [notificationId], references: [id], onDelete: Cascade)

  @@index([notificationId])
  @@index([event])
  @@index([timestamp])
}

model TelegramSubscription {
  id        String   @id @default(cuid())
  userId    String   @unique
  chatId    String   @unique
  username  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model TelegramLinkingCode {
  id        String   @id @default(cuid())
  code      String   @unique
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([expiresAt])
}

model ReportComment {
  id        String          @id @default(cuid())
  reportId  String
  userId    String
  content   String
  createdAt DateTime        @default(now())
  updatedAt DateTime        @updatedAt
  parentId  String?
  parent    ReportComment?  @relation("CommentReplies", fields: [parentId], references: [id])
  replies   ReportComment[] @relation("CommentReplies")
  report    Report          @relation(fields: [reportId], references: [id], onDelete: Cascade)
  user      User            @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([reportId])
  @@index([userId])
  @@index([parentId])
}
