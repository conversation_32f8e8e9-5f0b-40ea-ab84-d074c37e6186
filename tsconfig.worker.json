{"extends": "./tsconfig.json", "compilerOptions": {"module": "NodeNext", "moduleResolution": "NodeNext", "outDir": "dist", "target": "ES2020", "lib": ["ES2020"], "isolatedModules": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "noEmit": false, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["src/workers/**/*.ts", "src/lib/queue/**/*.ts", "src/utils/notificationTemplates.ts", "src/utils/notificationTargeting.ts", "src/utils/notificationTracking.ts", "src/lib/prisma.ts"], "exclude": ["node_modules"]}