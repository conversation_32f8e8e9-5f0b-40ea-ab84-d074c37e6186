{"name": "project", "version": "0.1.0", "private": true, "type": "module", "engines": {"node": ">=18.0.0"}, "scripts": {"dev": "next dev", "build": "next build", "build:production": "NODE_ENV=production next build --no-lint", "build:production:force": "bash $(pwd)/scripts/production-build.sh", "build:railway": "bash $(pwd)/scripts/railway-build.sh", "patch-deps": "node scripts/patch-dependencies.js", "deploy": "bash scripts/deploy-lightsail.sh", "build:deploy": "npm run build:production && npm run deploy", "build:deploy:no-lint": "NODE_ENV=production next build --no-lint && npm run deploy", "start": "next start", "start:production": "chmod +x scripts/start-production.sh && ./scripts/start-production.sh", "start:pm2": "bash scripts/pm2-start.sh", "stop:pm2": "bash scripts/pm2-stop.sh", "restart:pm2": "bash scripts/pm2-restart.sh", "lint": "next lint", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "setup": "node scripts/setup.cjs", "db:push": "prisma db push", "db:studio": "prisma studio", "db:migrate": "prisma migrate dev", "db:reset": "prisma migrate reset", "db:deploy": "prisma migrate deploy", "db:deploy:production": "prisma migrate deploy && npm run link-reports", "deploy:production": "npm install --legacy-peer-deps && npm run db:deploy:production && npm run build:production:force && npm run restart:pm2", "deploy:production:safe": "npm install --legacy-peer-deps && npm run typecheck && npm run db:deploy:production && npm run build:production:force && npm run restart:pm2", "test": "jest", "prepare": "husky install", "prisma:seed": "npx prisma db seed", "link-reports": "node scripts/link-plan-actual-reports.mjs", "typecheck": "tsc --noEmit", "generate-icons": "node scripts/generate-icons.js", "build:worker": "tsc --project tsconfig.worker.json && node scripts/fix-paths.cjs", "start:worker": "node scripts/start-notification-worker.js", "start:worker:standalone": "node scripts/start-standalone-worker.js", "start:standalone": "node --experimental-modules scripts/standalone-worker.js", "rebuild:worker": "npm run build:worker && npm run start:worker", "test:sqs": "node --loader ts-node/esm scripts/test-sqs.js", "test:send": "node --experimental-modules scripts/send-test-notification.js", "worker:start": "./scripts/pm2-start-worker.sh", "worker:stop": "./scripts/pm2-stop-worker.sh", "worker:logs": "pm2 logs notification-worker", "worker:status": "pm2 status notification-worker", "restart": "pkill -f 'node.*start' || true && npm run dev", "db:maintenance": "node -r dotenv/config dist/workers/db-maintenance.js", "migrate:comments": "node --loader ts-node/esm scripts/migrate-comments.js", "db:backup": "bash scripts/backup-database.sh", "db:restore": "bash scripts/restore-database.sh", "db:restore:modified": "bash scripts/restore-modified.sh", "fix:utf8": "bash scripts/fix-utf8-issues.sh"}, "dependencies": {"@auth/prisma-adapter": "^2.8.0", "@aws-sdk/client-s3": "^3.772.0", "@aws-sdk/client-sqs": "^3.775.0", "@aws-sdk/s3-request-presigner": "^3.772.0", "@hookform/resolvers": "^4.1.3", "@json2csv/plainjs": "^7.0.6", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.3.0", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@react-spring/web": "^9.7.5", "@types/node-telegram-bot-api": "^0.64.8", "@types/web-push": "^3.6.4", "@upstash/redis": "^1.34.5", "aws-sdk": "^2.1692.0", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dotenv": "^16.4.7", "esm": "^3.2.25", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "framer-motion": "^10.18.0", "jose": "^6.0.8", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.477.0", "next": "^15.3.1", "next-auth": "^4.24.11", "next-pwa": "^5.6.0", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "node-telegram-bot-api": "^0.66.0", "pdf-lib": "^1.17.1", "puppeteer": "^24.4.0", "react": "^19.0.0", "react-currency-input-field": "^3.0.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.54.2", "react-swipeable": "^7.0.1", "react-to-print": "^3.1.0", "recharts": "^2.15.1", "server-only": "^0.0.1", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.2", "web-push": "^3.6.7", "workbox-cacheable-response": "^7.3.0", "workbox-expiration": "^7.3.0", "workbox-precaching": "^7.3.0", "workbox-routing": "^7.3.0", "workbox-strategies": "^7.3.0", "xlsx": "^0.18.5", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@prisma/client": "^6.6.0", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^15.0.0", "@types/bcrypt": "^5.0.2", "@types/file-saver": "^2.0.7", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/node-fetch": "^2.6.11", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.16", "eslint": "^9", "eslint-config-next": "15.2.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.33.2", "husky": "^9.0.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.0.2", "null-loader": "^4.0.1", "postcss": "^8.4.31", "prettier": "^3.1.0", "prisma": "^6.6.0", "sharp": "^0.33.5", "tailwindcss": "^4", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "prisma": {"seed": "node --loader ts-node/esm prisma/seed.ts"}}