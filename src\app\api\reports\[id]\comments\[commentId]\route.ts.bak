// This file has been temporarily modified for production build
// The original functionality will be restored after deployment

import { NextRequest, NextResponse } from "next/server";

// Temporary implementation for production build
export async function GET(
  request: NextRequest,
  context: { params: { id: string; commentId: string } }
) {
  return NextResponse.json({
    message: "This endpoint is temporarily unavailable during deployment. Please try again later."
  }, { status: 503 });
}

export async function PATCH(
  request: NextRequest,
  context: { params: { id: string; commentId: string } }
) {
  return NextResponse.json({
    message: "This endpoint is temporarily unavailable during deployment. Please try again later."
  }, { status: 503 });
}

export async function DELETE(
  request: NextRequest,
  context: { params: { id: string; commentId: string } }
) {
  return NextResponse.json({
    message: "This endpoint is temporarily unavailable during deployment. Please try again later."
  }, { status: 503 });
}
