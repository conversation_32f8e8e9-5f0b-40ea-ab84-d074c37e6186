{"extends": ["next/core-web-vitals", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-object-type": "off", "react-hooks/exhaustive-deps": "off", "react/no-unescaped-entities": "off", "@next/next/no-img-element": "off", "prefer-const": "off", "no-var": "off", "@typescript-eslint/no-empty-interface": "off", "react-hooks/rules-of-hooks": "off"}, "ignorePatterns": ["node_modules/", ".next/", "public/", "*.config.js", "*.config.mjs"]}