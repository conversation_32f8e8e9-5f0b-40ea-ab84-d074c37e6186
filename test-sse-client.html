<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Advanced SSE Test Client</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 1000px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    
    h1, h2, h3 {
      color: #333;
    }
    
    .container {
      display: flex;
      gap: 20px;
    }
    
    .panel {
      flex: 1;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      background-color: #f9f9f9;
    }
    
    .events-container {
      height: 400px;
      overflow-y: auto;
      border: 1px solid #ccc;
      padding: 10px;
      margin-top: 10px;
      background-color: #fff;
    }
    
    .event {
      margin-bottom: 8px;
      padding: 8px;
      border-radius: 4px;
      border-left: 4px solid #ccc;
    }
    
    .event-connected { border-left-color: #28a745; background-color: #d4edda; }
    .event-notification { border-left-color: #007bff; background-color: #cce5ff; }
    .event-dashboardUpdate { border-left-color: #ffc107; background-color: #fff3cd; }
    .event-systemAlert { border-left-color: #dc3545; background-color: #f8d7da; }
    .event-ping { border-left-color: #6c757d; background-color: #f8f9fa; color: #6c757d; font-size: 0.9em; }
    .event-error { border-left-color: #dc3545; background-color: #f8d7da; }
    
    .controls {
      display: flex;
      gap: 10px;
      margin-bottom: 15px;
      flex-wrap: wrap;
    }
    
    button {
      padding: 8px 16px;
      cursor: pointer;
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      font-weight: bold;
    }
    
    button:hover {
      background-color: #0069d9;
    }
    
    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
    
    button.disconnect {
      background-color: #dc3545;
    }
    
    button.disconnect:hover {
      background-color: #c82333;
    }
    
    input, select {
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    
    .status {
      display: inline-block;
      padding: 5px 10px;
      border-radius: 4px;
      font-weight: bold;
    }
    
    .status-connected {
      background-color: #d4edda;
      color: #155724;
    }
    
    .status-disconnected {
      background-color: #f8d7da;
      color: #721c24;
    }
    
    .status-connecting {
      background-color: #fff3cd;
      color: #856404;
    }
    
    pre {
      white-space: pre-wrap;
      word-break: break-word;
    }
    
    .tabs {
      display: flex;
      border-bottom: 1px solid #ddd;
      margin-bottom: 15px;
    }
    
    .tab {
      padding: 10px 15px;
      cursor: pointer;
      border: 1px solid transparent;
      border-bottom: none;
      margin-bottom: -1px;
    }
    
    .tab.active {
      background-color: #fff;
      border-color: #ddd;
      border-radius: 4px 4px 0 0;
    }
    
    .tab-content {
      display: none;
    }
    
    .tab-content.active {
      display: block;
    }
    
    .stats {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 10px;
    }
    
    .stat-card {
      background-color: #fff;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 10px;
    }
    
    .stat-value {
      font-size: 1.5em;
      font-weight: bold;
      color: #007bff;
    }
    
    .code-block {
      background-color: #f8f9fa;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      margin: 10px 0;
      overflow-x: auto;
    }
    
    .event-filter {
      margin-bottom: 10px;
    }
    
    .event-filter label {
      margin-right: 10px;
    }
  </style>
</head>
<body>
  <h1>Advanced SSE Test Client</h1>
  
  <div class="tabs">
    <div class="tab active" data-tab="client">Client</div>
    <div class="tab" data-tab="documentation">Documentation</div>
    <div class="tab" data-tab="implementation">Implementation</div>
  </div>
  
  <div class="tab-content active" id="client-tab">
    <div class="container">
      <div class="panel">
        <h2>Connection</h2>
        <div class="controls">
          <button id="connect">Connect</button>
          <button id="disconnect" class="disconnect" disabled>Disconnect</button>
          <span id="status" class="status status-disconnected">Disconnected</span>
        </div>
        
        <h3>Connection Options</h3>
        <div class="controls">
          <select id="endpoint">
            <option value="/sse">Default SSE Endpoint</option>
            <option value="/api/sse">API SSE Endpoint</option>
            <option value="/api/dashboard/sse">Dashboard SSE Endpoint</option>
          </select>
          <label>
            <input type="checkbox" id="autoReconnect" checked> Auto Reconnect
          </label>
        </div>
        
        <h3>Send Event</h3>
        <div class="controls">
          <select id="eventType">
            <option value="notification">Notification</option>
            <option value="dashboardUpdate">Dashboard Update</option>
            <option value="systemAlert">System Alert</option>
          </select>
          <input id="eventMessage" placeholder="Event message" value="Test message">
          <button id="sendEvent">Send Event</button>
        </div>
        
        <h3>Statistics</h3>
        <div class="stats">
          <div class="stat-card">
            <div>Events Received</div>
            <div id="eventsReceived" class="stat-value">0</div>
          </div>
          <div class="stat-card">
            <div>Connection Time</div>
            <div id="connectionTime" class="stat-value">0s</div>
          </div>
          <div class="stat-card">
            <div>Last Activity</div>
            <div id="lastActivity" class="stat-value">-</div>
          </div>
          <div class="stat-card">
            <div>Reconnections</div>
            <div id="reconnectionCount" class="stat-value">0</div>
          </div>
        </div>
      </div>
      
      <div class="panel">
        <h2>Events</h2>
        <div class="event-filter">
          <label>
            <input type="checkbox" class="event-type-filter" data-type="all" checked> All
          </label>
          <label>
            <input type="checkbox" class="event-type-filter" data-type="connected" checked> Connected
          </label>
          <label>
            <input type="checkbox" class="event-type-filter" data-type="notification" checked> Notification
          </label>
          <label>
            <input type="checkbox" class="event-type-filter" data-type="dashboardUpdate" checked> Dashboard
          </label>
          <label>
            <input type="checkbox" class="event-type-filter" data-type="ping" checked> Ping
          </label>
        </div>
        <div id="events" class="events-container"></div>
      </div>
    </div>
  </div>
  
  <div class="tab-content" id="documentation-tab">
    <h2>SSE Documentation</h2>
    
    <h3>What is SSE?</h3>
    <p>
      Server-Sent Events (SSE) is a technology that allows a server to push updates to clients over a single HTTP connection.
      Unlike WebSockets, SSE is a one-way communication channel from server to client, making it simpler to implement for
      scenarios where you only need server-to-client updates.
    </p>
    
    <h3>Key Features</h3>
    <ul>
      <li><strong>One-way communication:</strong> Server pushes data to clients</li>
      <li><strong>Simple protocol:</strong> Uses standard HTTP</li>
      <li><strong>Automatic reconnection:</strong> Browsers handle reconnection automatically</li>
      <li><strong>Event types:</strong> Support for named events</li>
      <li><strong>Native browser support:</strong> No additional libraries needed on the client</li>
    </ul>
    
    <h3>SSE vs WebSockets</h3>
    <table border="1" cellpadding="8" cellspacing="0" style="border-collapse: collapse; width: 100%;">
      <tr>
        <th>Feature</th>
        <th>SSE</th>
        <th>WebSockets</th>
      </tr>
      <tr>
        <td>Communication</td>
        <td>One-way (server to client)</td>
        <td>Two-way (full-duplex)</td>
      </tr>
      <tr>
        <td>Protocol</td>
        <td>HTTP</td>
        <td>WebSocket protocol (ws:// or wss://)</td>
      </tr>
      <tr>
        <td>Reconnection</td>
        <td>Automatic</td>
        <td>Manual implementation required</td>
      </tr>
      <tr>
        <td>Message types</td>
        <td>Text only</td>
        <td>Text and binary</td>
      </tr>
      <tr>
        <td>Browser support</td>
        <td>Good (IE needs polyfill)</td>
        <td>Excellent</td>
      </tr>
      <tr>
        <td>Proxy handling</td>
        <td>Works with standard proxies</td>
        <td>May require special configuration</td>
      </tr>
    </table>
    
    <h3>When to Use SSE</h3>
    <ul>
      <li>Real-time dashboards</li>
      <li>News feeds and social media updates</li>
      <li>Notifications</li>
      <li>Stock tickers</li>
      <li>Log monitoring</li>
    </ul>
    
    <h3>Basic SSE Implementation</h3>
    
    <h4>Server-side (Node.js)</h4>
    <div class="code-block">
      <pre><code>// Set SSE headers
res.writeHead(200, {
  'Content-Type': 'text/event-stream',
  'Cache-Control': 'no-cache',
  'Connection': 'keep-alive'
});

// Send an event
res.write(`event: notification\n`);
res.write(`data: ${JSON.stringify({ message: "Hello SSE!" })}\n\n`);</code></pre>
    </div>
    
    <h4>Client-side (JavaScript)</h4>
    <div class="code-block">
      <pre><code>// Create EventSource
const eventSource = new EventSource('/sse');

// Listen for all messages
eventSource.onmessage = (event) => {
  console.log('Received message:', JSON.parse(event.data));
};

// Listen for specific event type
eventSource.addEventListener('notification', (event) => {
  console.log('Notification:', JSON.parse(event.data));
});</code></pre>
    </div>
  </div>
  
  <div class="tab-content" id="implementation-tab">
    <h2>SSE Implementation Details</h2>
    
    <h3>Server-Side Implementation</h3>
    <p>
      Our SSE implementation uses a handler class to manage connections and send events.
      The handler maintains a list of connected clients and provides methods to send events
      to specific users or broadcast to all clients.
    </p>
    
    <div class="code-block">
      <pre><code>// SSE Handler (simplified)
class SSEHandler {
  private clients: Map<string, Client> = new Map();
  
  // Add a client connection
  addClient(id: string, userId: string, response: any) {
    this.clients.set(id, { id, userId, response, connectedAt: Date.now() });
    return id;
  }
  
  // Remove a client connection
  removeClient(id: string) {
    this.clients.delete(id);
  }
  
  // Send an event to a specific user
  sendEventToUser(userId: string, eventType: string, data: any) {
    for (const client of this.clients.values()) {
      if (client.userId === userId) {
        this.sendEvent(client.response, { type: eventType, data });
      }
    }
  }
  
  // Send a properly formatted SSE event
  private sendEvent(response: any, event: SSEEvent) {
    let message = '';
    if (event.type) message += `event: ${event.type}\n`;
    message += `data: ${JSON.stringify(event.data)}\n\n`;
    response.write(message);
  }
}</code></pre>
    </div>
    
    <h3>API Route Implementation</h3>
    <p>
      The SSE API route establishes the connection and sets up the event stream.
    </p>
    
    <div class="code-block">
      <pre><code>// SSE API Route (simplified)
export async function GET(req: NextRequest) {
  // Authenticate the request
  const userId = "user-123"; // In real code, get from auth
  
  // Create SSE stream
  const stream = new ReadableStream({
    start(controller) {
      const encoder = new TextEncoder();
      const clientId = crypto.randomUUID();
      
      // Create response object
      const response = {
        write: (chunk: string) => controller.enqueue(encoder.encode(chunk)),
      };
      
      // Register client with SSE handler
      sseHandler.addClient(clientId, userId, response);
      
      // Send initial connected event
      sseHandler.sendEventToUser(userId, "connected", { 
        message: "Connected to SSE server" 
      });
      
      // Set up ping interval
      const pingInterval = setInterval(() => {
        sseHandler.sendEventToUser(userId, "ping", { time: new Date().toISOString() });
      }, 30000);
      
      // Handle connection close
      req.signal.addEventListener("abort", () => {
        clearInterval(pingInterval);
        sseHandler.removeClient(clientId);
      });
    }
  });
  
  // Return the SSE response
  return new Response(stream, {
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      "Connection": "keep-alive",
    },
  });
}</code></pre>
    </div>
    
    <h3>Client-Side Hook Implementation</h3>
    <p>
      The client-side hook provides a standardized way to connect to SSE endpoints.
    </p>
    
    <div class="code-block">
      <pre><code>// useSSE hook (simplified)
export function useSSE(options = {}) {
  const {
    endpoint = '/api/sse',
    autoReconnect = true,
    eventHandlers = {}
  } = options;
  
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState(null);
  const [lastEvent, setLastEvent] = useState(null);
  const eventSourceRef = useRef(null);
  
  useEffect(() => {
    // Create EventSource
    const eventSource = new EventSource(endpoint);
    eventSourceRef.current = eventSource;
    
    // Connection opened
    eventSource.onopen = () => {
      setIsConnected(true);
      setError(null);
    };
    
    // Connection error
    eventSource.onerror = (error) => {
      setIsConnected(false);
      setError(error);
      
      // Close the connection on error
      eventSource.close();
      
      // Reconnect if enabled
      if (autoReconnect) {
        setTimeout(() => {
          // Reconnect logic
        }, 3000);
      }
    };
    
    // Set up event handlers
    Object.entries(eventHandlers).forEach(([eventType, handler]) => {
      eventSource.addEventListener(eventType, (event) => {
        const data = JSON.parse(event.data);
        setLastEvent({ type: eventType, data });
        handler(data);
      });
    });
    
    // Clean up on unmount
    return () => {
      eventSource.close();
    };
  }, [endpoint, autoReconnect]);
  
  return { isConnected, error, lastEvent };
}</code></pre>
    </div>
  </div>
  
  <script>
    // Global variables
    let eventSource = null;
    let connectionStartTime = null;
    let eventsReceived = 0;
    let reconnectionCount = 0;
    let connectionTimer = null;
    
    // DOM elements
    const eventsContainer = document.getElementById('events');
    const statusElement = document.getElementById('status');
    const connectButton = document.getElementById('connect');
    const disconnectButton = document.getElementById('disconnect');
    const eventsReceivedElement = document.getElementById('eventsReceived');
    const connectionTimeElement = document.getElementById('connectionTime');
    const lastActivityElement = document.getElementById('lastActivity');
    const reconnectionCountElement = document.getElementById('reconnectionCount');
    
    // Tab functionality
    document.querySelectorAll('.tab').forEach(tab => {
      tab.addEventListener('click', () => {
        // Remove active class from all tabs and tab contents
        document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
        
        // Add active class to clicked tab and corresponding content
        tab.classList.add('active');
        document.getElementById(`${tab.dataset.tab}-tab`).classList.add('active');
      });
    });
    
    // Event filtering
    document.querySelectorAll('.event-type-filter').forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        const type = checkbox.dataset.type;
        const checked = checkbox.checked;
        
        if (type === 'all') {
          // Check/uncheck all other checkboxes
          document.querySelectorAll('.event-type-filter:not([data-type="all"])').forEach(cb => {
            cb.checked = checked;
          });
          
          // Show/hide all events
          document.querySelectorAll('.event').forEach(event => {
            event.style.display = checked ? 'block' : 'none';
          });
        } else {
          // Show/hide events of this type
          document.querySelectorAll(`.event-${type}`).forEach(event => {
            event.style.display = checked ? 'block' : 'none';
          });
          
          // Update "All" checkbox based on other checkboxes
          const allChecked = Array.from(
            document.querySelectorAll('.event-type-filter:not([data-type="all"])')
          ).every(cb => cb.checked);
          
          document.querySelector('.event-type-filter[data-type="all"]').checked = allChecked;
        }
      });
    });
    
    // Connect to SSE endpoint
    connectButton.addEventListener('click', () => {
      if (eventSource) {
        console.log('Already connected');
        return;
      }
      
      // Get connection options
      const endpoint = document.getElementById('endpoint').value;
      const autoReconnect = document.getElementById('autoReconnect').checked;
      
      // Update UI
      statusElement.textContent = 'Connecting...';
      statusElement.className = 'status status-connecting';
      connectButton.disabled = true;
      disconnectButton.disabled = false;
      
      // Reset statistics
      connectionStartTime = Date.now();
      eventsReceived = 0;
      eventsReceivedElement.textContent = '0';
      lastActivityElement.textContent = '-';
      
      // Start connection timer
      connectionTimer = setInterval(() => {
        if (connectionStartTime) {
          const seconds = Math.floor((Date.now() - connectionStartTime) / 1000);
          connectionTimeElement.textContent = `${seconds}s`;
        }
      }, 1000);
      
      // Create new EventSource
      eventSource = new EventSource(endpoint);
      
      // Connection opened
      eventSource.onopen = () => {
        statusElement.textContent = 'Connected';
        statusElement.className = 'status status-connected';
        addEvent('system', { message: 'Connection established' }, 'event-connected');
      };
      
      // Connection error
      eventSource.onerror = (error) => {
        console.error('SSE connection error:', error);
        statusElement.textContent = 'Error - Reconnecting...';
        statusElement.className = 'status status-disconnected';
        addEvent('system', { message: 'Connection error - attempting to reconnect' }, 'event-error');
        
        // Close the connection
        eventSource.close();
        eventSource = null;
        
        // Reconnect if enabled
        if (autoReconnect) {
          reconnectionCount++;
          reconnectionCountElement.textContent = reconnectionCount.toString();
          
          setTimeout(() => {
            connectButton.click();
          }, 3000);
        } else {
          // Update UI for disconnected state
          statusElement.textContent = 'Disconnected (Error)';
          statusElement.className = 'status status-disconnected';
          connectButton.disabled = false;
          disconnectButton.disabled = true;
          clearInterval(connectionTimer);
        }
      };
      
      // Listen for messages (unnamed events)
      eventSource.onmessage = (event) => {
        console.log('Received message:', event.data);
        try {
          const data = JSON.parse(event.data);
          addEvent('message', data);
        } catch (e) {
          addEvent('message', { raw: event.data });
        }
      };
      
      // Listen for specific event types
      const eventTypes = ['connected', 'notification', 'dashboardUpdate', 'systemAlert', 'ping'];
      
      eventTypes.forEach(eventType => {
        eventSource.addEventListener(eventType, (event) => {
          console.log(`${eventType} event:`, event.data);
          try {
            const data = JSON.parse(event.data);
            addEvent(eventType, data, `event-${eventType}`);
          } catch (e) {
            addEvent(eventType, { raw: event.data }, `event-${eventType}`);
          }
        });
      });
    });
    
    // Disconnect from SSE endpoint
    disconnectButton.addEventListener('click', () => {
      if (eventSource) {
        eventSource.close();
        eventSource = null;
        
        // Update UI
        statusElement.textContent = 'Disconnected';
        statusElement.className = 'status status-disconnected';
        connectButton.disabled = false;
        disconnectButton.disabled = true;
        clearInterval(connectionTimer);
        
        addEvent('system', { message: 'Disconnected from server' }, 'event-system');
      }
    });
    
    // Send custom event
    document.getElementById('sendEvent').addEventListener('click', () => {
      const eventType = document.getElementById('eventType').value;
      const message = document.getElementById('eventMessage').value;
      
      fetch(`/trigger-event?type=${encodeURIComponent(eventType)}&message=${encodeURIComponent(message)}`)
        .then(response => response.json())
        .then(data => {
          console.log('Event triggered:', data);
          if (!data.success) {
            addEvent('system', { message: `Failed to send event: ${data.message}` }, 'event-error');
          }
        })
        .catch(error => {
          console.error('Error triggering event:', error);
          addEvent('system', { message: `Error: ${error.message}` }, 'event-error');
        });
    });
    
    // Helper function to add event to the UI
    function addEvent(type, data, className = '') {
      // Update statistics
      eventsReceived++;
      eventsReceivedElement.textContent = eventsReceived.toString();
      lastActivityElement.textContent = new Date().toLocaleTimeString();
      
      // Create event element
      const eventElement = document.createElement('div');
      eventElement.className = `event ${className || 'event-' + type}`;
      
      const timestamp = new Date().toLocaleTimeString();
      const formattedData = JSON.stringify(data, null, 2);
      
      eventElement.innerHTML = `
        <strong>${timestamp} - ${type}</strong>
        <pre>${formattedData}</pre>
      `;
      
      eventsContainer.prepend(eventElement);
      
      // Check if this event type is filtered out
      const typeFilter = document.querySelector(`.event-type-filter[data-type="${type}"]`);
      if (typeFilter && !typeFilter.checked) {
        eventElement.style.display = 'none';
      }
    }
  </script>
</body>
</html>
