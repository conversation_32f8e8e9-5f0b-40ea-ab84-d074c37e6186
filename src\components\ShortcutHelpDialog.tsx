import {
    Dialog,
    DialogContent,
    <PERSON>alogDescription,
    Di<PERSON><PERSON>eader,
    <PERSON><PERSON>Title,
    DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Keyboard } from "lucide-react";

interface ShortcutDescription {
    keys: string;
    description: string;
}

interface ShortcutHelpDialogProps {
    shortcuts: ShortcutDescription[];
}

export function ShortcutHelpDialog({ shortcuts }: ShortcutHelpDialogProps) {
    return (
        <Dialog>
            <DialogTrigger asChild>
                <Button
                    variant="outline"
                    size="sm"
                    className="gap-2"
                    aria-label="View keyboard shortcuts"
                >
                    <Keyboard className="h-4 w-4" />
                    <span className="hidden sm:inline">Keyboard Shortcuts</span>
                </Button>
            </DialogTrigger>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>Keyboard Shortcuts</DialogTitle>
                    <DialogDescription>
                        Use these keyboard shortcuts to quickly navigate and perform actions
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    {shortcuts.map((shortcut, index) => (
                        <div
                            key={index}
                            className="flex items-center justify-between px-4"
                        >
                            <span className="text-sm text-muted-foreground">
                                {shortcut.description}
                            </span>
                            <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground">
                                {shortcut.keys}
                            </kbd>
                        </div>
                    ))}
                </div>
            </DialogContent>
        </Dialog>
    );
}