{"$schema": "https://railway.com/railway.schema.json", "build": {"builder": "NIXPACKS", "buildCommand": "npm run build:railway", "watchPatterns": ["src/**", "public/**", "prisma/**"]}, "deploy": {"runtime": "V2", "numReplicas": 1, "sleepApplication": false, "multiRegionConfig": {"asia-southeast1-eqsg3a": {"numReplicas": 1}}, "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 10, "startCommand": "pm2-runtime ecosystem.production.config.cjs"}}